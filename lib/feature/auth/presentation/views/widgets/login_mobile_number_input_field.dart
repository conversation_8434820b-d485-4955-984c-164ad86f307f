import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/core/widgets/custom_error_toast.dart';
import 'package:gather_point/feature/auth/presentation/Manager/login_cubit/login_cubit.dart';
import 'package:gather_point/generated/l10n.dart';

class LoginMobileNumberInputField extends StatefulWidget {
  const LoginMobileNumberInputField({
    super.key,
  });

  @override
  State<LoginMobileNumberInputField> createState() =>
      _LoginMobileNumberInputFieldState();
}

class _LoginMobileNumberInputFieldState
    extends State<LoginMobileNumberInputField> {

  @override
  Widget build(BuildContext context) {
    final loginCubit = BlocProvider.of<LoginCubit>(context);
    final s = S.of(context);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.black,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.yellow),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: [
            const SizedBox(width: 8),
            SvgPicture.asset(AppAssets.imagesSaudiArabia),
            const SizedBox(width: 4),
            Text(
              '966+',
              style: AppTextStyles.font16Bold.copyWith(
                color: AppColors.white.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(width: 8),
            const VerticalDivider(
              color: AppColors.lightGrey8,
              thickness: 1,
              width: 1,
              indent: 12,
              endIndent: 12,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: loginCubit.phoneController,
                textAlign: TextAlign.end,
                style: AppTextStyles.font16Bold.copyWith(
                  color: AppColors.white,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                cursorColor: AppColors.yellow,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  hintText: s.enterPhoneNumber,
                  hintStyle: AppTextStyles.font16Bold.copyWith(
                    color: AppColors.white.withValues(alpha: 0.6),
                  ),
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(width: 8),
            PrimaryButton(
              fullWidth: false,
              onPressed: () {
                SoundManager.playClickSound();
                final phoneText = loginCubit.phoneController.text.trim();

                if (phoneText.isEmpty) {
                  showCustomErrorToast(s.pleaseEnterPhoneNumber);
                  return;
                } else if (!RegExp(
                        r'^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$')
                    .hasMatch(phoneText)) {
                  showCustomErrorToast(s.pleaseCheckPhoneNumber);
                  return;
                } else {
                  loginCubit.login();
                }
              },
              label: s.login,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              borderRadius: 20,
            )
          ],
        ),
      ),
    );
  }
}
