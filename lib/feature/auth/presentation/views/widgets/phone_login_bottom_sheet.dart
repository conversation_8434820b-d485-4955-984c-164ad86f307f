import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/core/widgets/custom_error_toast.dart';
import 'package:gather_point/feature/auth/presentation/Manager/login_cubit/login_cubit.dart';
import 'package:gather_point/generated/l10n.dart';

class PhoneLoginBottomSheet extends StatefulWidget {
  const PhoneLoginBottomSheet({super.key});

  @override
  State<PhoneLoginBottomSheet> createState() => _PhoneLoginBottomSheetState();
}

class _PhoneLoginBottomSheetState extends State<PhoneLoginBottomSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _closeBottomSheet() {
    _animationController.reverse().then((_) {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final loginCubit = BlocProvider.of<LoginCubit>(context);
    final s = S.of(context);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5 * _fadeAnimation.value),
          ),
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(_slideAnimation),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: AppColors.black,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.lightGrey8,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    
                    // Header
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: _closeBottomSheet,
                            icon: const Icon(
                              Icons.close,
                              color: AppColors.white,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              s.proceedWithPhone,
                              style: AppTextStyles.font20Bold.copyWith(
                                color: AppColors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Phone input section
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.darkGrey,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.yellow.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.enterPhoneNumber,
                              style: AppTextStyles.font14Bold.copyWith(
                                color: AppColors.white.withValues(alpha: 0.7),
                              ),
                            ),
                            const SizedBox(height: 12),
                            
                            // Phone input field
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.black,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.yellow.withValues(alpha: 0.5),
                                ),
                              ),
                              child: Directionality(
                                textDirection: TextDirection.ltr,
                                child: Row(
                                  children: [
                                    const SizedBox(width: 16),
                                    SvgPicture.asset(
                                      AppAssets.imagesSaudiArabia,
                                      width: 24,
                                      height: 24,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      '+966',
                                      style: AppTextStyles.font16Bold.copyWith(
                                        color: AppColors.white,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Container(
                                      width: 1,
                                      height: 24,
                                      color: AppColors.lightGrey8,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: TextField(
                                        controller: loginCubit.phoneController,
                                        textAlign: TextAlign.left,
                                        textDirection: TextDirection.ltr,
                                        style: AppTextStyles.font16Bold.copyWith(
                                          color: AppColors.white,
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.digitsOnly,
                                          LengthLimitingTextInputFormatter(9),
                                        ],
                                        cursorColor: AppColors.yellow,
                                        keyboardType: TextInputType.phone,
                                        autofocus: true,
                                        decoration: InputDecoration(
                                          hintText: '5XXXXXXXX',
                                          hintStyle: AppTextStyles.font16Bold.copyWith(
                                            color: AppColors.white.withValues(alpha: 0.4),
                                          ),
                                          border: InputBorder.none,
                                          contentPadding: const EdgeInsets.symmetric(
                                            vertical: 16,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Login button
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: PrimaryButton(
                        onPressed: () {
                          SoundManager.playClickSound();
                          final phoneText = loginCubit.phoneController.text.trim();

                          if (phoneText.isEmpty) {
                            showCustomErrorToast(s.pleaseEnterPhoneNumber);
                            return;
                          } else if (!RegExp(
                                  r'^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$')
                              .hasMatch(phoneText)) {
                            showCustomErrorToast(s.pleaseCheckPhoneNumber);
                            return;
                          } else {
                            loginCubit.login();
                            _closeBottomSheet();
                          }
                        },
                        label: s.login,
                        backgroundColor: AppColors.yellow,
                        textStyle: const TextStyle(
                          color: AppColors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // Bottom safe area
                    SizedBox(height: MediaQuery.of(context).padding.bottom),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Future<void> show(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const PhoneLoginBottomSheet(),
    );
  }
}
