import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_guest_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:equatable/equatable.dart';

part 'app_initialization_state.dart';

class AppInitializationCubit extends Cubit<AppInitializationState> {
  final LoginGuestUseCase loginGuestUseCase;
  final LoginTokenUseCase loginTokenUseCase;

  AppInitializationCubit({
    required this.loginGuestUseCase,
    required this.loginTokenUseCase,
  }) : super(AppInitializationInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(AppInitializationState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Initialize the app with proper flow
  Future<void> initializeApp() async {
    _safeEmit(AppInitializationLoading());

    try {
      // Check if user has viewed onboarding
      final settingsBox = Hive.box<bool>(AppConstants.kSettingsBoxName);
      final hasViewedOnboarding = settingsBox.get(AppConstants.kOnboardingCompletedKey) ?? false;

      if (!hasViewedOnboarding) {
        // First time user - show onboarding
        _safeEmit(AppInitializationShowOnboarding());
        return;
      }

      // User has viewed onboarding - check for existing token
      final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
      final existingUser = profileBox.get(AppConstants.kMyProfileKey);

      if (existingUser?.token != null && existingUser!.token.isNotEmpty) {
        // Validate existing token
        await _validateExistingToken(existingUser);
      } else {
        // No token - generate guest token and navigate to home
        await _generateGuestTokenAndNavigate();
      }
    } catch (e) {
      // On any error, generate guest token and navigate to home
      await _generateGuestTokenAndNavigate();
    }
  }

  /// Complete onboarding and generate guest token
  Future<void> completeOnboarding() async {
    _safeEmit(AppInitializationLoading());

    try {
      // Mark onboarding as completed
      final settingsBox = Hive.box<bool>(AppConstants.kSettingsBoxName);
      await settingsBox.put(AppConstants.kOnboardingCompletedKey, true);

      // Generate guest token and navigate to home
      await _generateGuestTokenAndNavigate();
    } catch (e) {
      _safeEmit(AppInitializationError('Failed to complete onboarding: ${e.toString()}'));
    }
  }

  /// Validate existing token
  Future<void> _validateExistingToken(UserEntity user) async {
    try {
      // Check if the existing user is a guest
      if (user.isGuest) {
        // For guest users, always generate a new guest token instead of validating the old one
        await _generateGuestTokenAndNavigate();
        return;
      }

      // For non-guest users, validate their login token
      final result = await loginTokenUseCase.call(user.token);

      result.fold(
        (failure) async {
          // Token is invalid - generate new guest token
          await _generateGuestTokenAndNavigate();
        },
        (validUser) {
          // Token is valid - navigate to home
          _safeEmit(AppInitializationNavigateToHome(user: validUser));
        },
      );
    } catch (e) {
      // On error, generate guest token
      await _generateGuestTokenAndNavigate();
    }
  }

  /// Generate guest token and navigate to home
  Future<void> _generateGuestTokenAndNavigate() async {
    try {
      final result = await loginGuestUseCase.call('');
      
      result.fold(
        (failure) {
          _safeEmit(AppInitializationError('Failed to generate guest token: ${failure.errMessage}'));
        },
        (guestUser) {
          // Save guest user to storage
          final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
          profileBox.put(AppConstants.kMyProfileKey, guestUser);

          // Navigate to home
          _safeEmit(AppInitializationNavigateToHome(user: guestUser));
        },
      );
    } catch (e) {
      _safeEmit(AppInitializationError('Failed to initialize app: ${e.toString()}'));
    }
  }

  /// Force refresh token (for manual refresh scenarios)
  Future<void> refreshToken() async {
    _safeEmit(AppInitializationLoading());
    if (!isClosed) {
      await _generateGuestTokenAndNavigate();
    }
  }

  /// Reset app state (for logout scenarios)
  Future<void> resetAppState() async {
    try {
      // Clear user data
      final profileBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
      await profileBox.clear();

      // Generate new guest token (only if cubit is still active)
      if (!isClosed) {
        await _generateGuestTokenAndNavigate();
      }
    } catch (e) {
      _safeEmit(AppInitializationError('Failed to reset app state: ${e.toString()}'));
    }
  }
}
